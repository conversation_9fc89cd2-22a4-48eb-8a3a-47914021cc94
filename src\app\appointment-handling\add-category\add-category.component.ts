import { CommonModule } from '@angular/common';
import { HttpClient, HttpClientModule } from '@angular/common/http';
import { Component, OnInit } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { HeaderAdminSectionComponent } from "../header-admin-section/header-admin-section.component";
import { environment } from '../../../environments/environment';
import { catchError, throwError } from 'rxjs';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'app-add-category',
  standalone: true,
  imports: [FormsModule, CommonModule, HttpClientModule, HeaderAdminSectionComponent, RouterLink],
  templateUrl: './add-category.component.html',
  styleUrl: './add-category.component.css'
})
export class AddCategoryComponent implements OnInit {
  public category: any = {
    description: ""
  };

  constructor(private http: HttpClient) {}

  ngOnInit(): void {
    document.body.style.backgroundColor = '#E6FFE8';
    document.body.style.overflowY= 'hidden';
    document.body.style.overflowX= 'hidden';

  }


  public addCategory(){
    this.http.post(`${environment.CATEGORY_BASE_URL}/add`, this.category, { responseType: 'text' }).pipe(
      catchError(error => {
        alert("Error.")
        return throwError(() => new Error('Error fetching data.'));
      })
    ).subscribe((data) => {
      alert(data);
    });
  }

  public getLocalDate() {
    let date = new Date();
    let localDate = date.toLocaleDateString();
    return localDate;
  }
}
