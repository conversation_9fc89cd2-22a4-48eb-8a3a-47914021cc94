interface Environment {
    APPOINTMENT_MANAGEMENT_BASE_URL: string;
    CATEGORY_BASE_URL: string;
    PATIENT_MANAGEMENT_BASE_URL: string;
    PRESCRIPTION_MANAGEMENT_BASE_URL: string;
    RECORD_MANAGEMENT_BASE_URL: string;
    AUTH_URL:string;
}

export const environment: Environment = {
    APPOINTMENT_MANAGEMENT_BASE_URL: 'https://7dacf2bbb95662717d239e7232c35b68.loophole.site/appointment',
    CATEGORY_BASE_URL: 'https://7dacf2bbb95662717d239e7232c35b68.loophole.site/category',
    PATIENT_MANAGEMENT_BASE_URL: 'https://7dacf2bbb95662717d239e7232c35b68.loophole.site/patient',
    PRESCRIPTION_MANAGEMENT_BASE_URL: 'https://7dacf2bbb95662717d239e7232c35b68.loophole.site/api/v1/prescription',
    RECORD_MANAGEMENT_BASE_URL: 'https://7dacf2bbb95662717d239e7232c35b68.loophole.site/record',
    AUTH_URL:'https://a4ad5ce0df3724e1e9f0133213e8a9a1.loophole.site'
  };

