import { ErrorPageComponent } from './error-page/error-page.component';
import { SinupComponent } from './sinup/sinup.component';
import { LogComponent } from './log/log.component';
import { DashboardComponent } from './dashboard/dashboard.component';
import { Routes } from '@angular/router';
import { AuthGuard } from './core/guard/auth.guard';
import { UserRole } from './models/auth/login.model';
import { UpdateComponent } from './patient-management/update/update.component';
import { ViewRecordsComponent } from './medical-records-handling/view-records/view-records.component';
import { ViewReportsComponent } from './medical-records-handling/view-reports/view-reports.component';
import { AddRecordsComponent } from './medical-records-handling/add-records/add-records.component';
import { DashBordPageComponent } from './dash-bord-page/dash-bord-page.component';
import { ViewPatientListComponent } from './patient-management/view-patient-list/view-patient-list.component';
import { SearchComponent } from './patient-management/search/search.component';
import { AddComponent } from './patient-management/add/add.component';
import { AddAppointmentComponent } from './appointment-handling/add-appointment/add-appointment.component';
import { DeleteAppointmentComponent } from './appointment-handling/delete-appointment/delete-appointment.component';
import { AppointmentViewComponent } from './appointment-handling/appointment-view/appointment-view.component';
import { AppoimentSearchComponent } from './appointment-handling/appoiment-search/appoiment-search.component';
import { PrescriptionManagementComponent } from './prescription-management/prescription-management.component';
import { UpdateAppointmentComponent } from './appointment-handling/update-appointment/update-appointment.component';
import { AddCategoryComponent } from './appointment-handling/add-category/add-category.component'
import { UpdateCategoryComponent } from './appointment-handling/update-category/update-category.component'
import { ViewCategoryComponent } from './appointment-handling/view-category/view-category.component'
import { DoctorsComponent } from './doctors/./doctors.component'
import { SettingsComponent } from './settings/./settings.component'

export const routes: Routes = [
  // Public routes (no authentication required)
  { path: 'log', component: LogComponent },
  { path: 'sinup', component: SinupComponent },
  { path: 'error-page', component: ErrorPageComponent },

  // Protected routes (authentication required)
  {
    path: '',
    component: DashBordPageComponent,
    canActivate: [AuthGuard],
    children: [
      // Default dashboard route
      {
        path: '',
        component: DashboardComponent
      },

      // Doctor routes
      {
        path: 'dashboard',
        component: DashboardComponent,
        canActivate: [AuthGuard],
        data: { roles: [UserRole.DOCTOR] }
      },

      // Patient Management routes (accessible by DOCTOR and OPD_ADMIN)
      {
        path: 'view-patient',
        component: ViewPatientListComponent,
        canActivate: [AuthGuard],
        data: { roles: [UserRole.DOCTOR, UserRole.OPD_ADMIN] }
      },
      {
        path: 'search-patient',
        component: SearchComponent,
        canActivate: [AuthGuard],
        data: { roles: [UserRole.DOCTOR, UserRole.OPD_ADMIN] }
      },
      {
        path: 'add-patient',
        component: AddComponent,
        canActivate: [AuthGuard],
        data: { roles: [UserRole.DOCTOR, UserRole.OPD_ADMIN] }
      },
      {
        path: 'update-patient',
        component: UpdateComponent,
        canActivate: [AuthGuard],
        data: { roles: [UserRole.DOCTOR, UserRole.OPD_ADMIN] }
      },

      // Appointment Management routes (primarily for OPD_ADMIN)
      {
        path: 'appointment-handling',
        component: AppointmentViewComponent,
        canActivate: [AuthGuard],
        data: { roles: [UserRole.OPD_ADMIN] }
      },
      {
        path: 'add-appointment',
        component: AddAppointmentComponent,
        canActivate: [AuthGuard],
        data: { roles: [UserRole.OPD_ADMIN] }
      },
      {
        path: 'delete-appointment',
        component: DeleteAppointmentComponent,
        canActivate: [AuthGuard],
        data: { roles: [UserRole.OPD_ADMIN] }
      },
      {
        path: 'update-appointment',
        component: UpdateAppointmentComponent,
        canActivate: [AuthGuard],
        data: { roles: [UserRole.OPD_ADMIN] }
      },
      {
        path: 'view-appointment',
        component: AppointmentViewComponent,
        canActivate: [AuthGuard],
        data: { roles: [UserRole.OPD_ADMIN, UserRole.DOCTOR] }
      },
      {
        path: 'search-appointment',
        component: AppoimentSearchComponent,
        canActivate: [AuthGuard],
        data: { roles: [UserRole.OPD_ADMIN, UserRole.DOCTOR] }
      },

      // Category Management routes (for OPD_ADMIN)
      {
        path: 'add-category',
        component: AddCategoryComponent,
        canActivate: [AuthGuard],
        data: { roles: [UserRole.OPD_ADMIN] }
      },
      {
        path: 'update-category',
        component: UpdateCategoryComponent,
        canActivate: [AuthGuard],
        data: { roles: [UserRole.OPD_ADMIN] }
      },
      {
        path: 'view-category',
        component: ViewCategoryComponent,
        canActivate: [AuthGuard],
        data: { roles: [UserRole.OPD_ADMIN] }
      },

      // Prescription Management routes (for PRESCRIPTION_ADMIN)
      {
        path: 'prescription-management',
        component: PrescriptionManagementComponent,
        canActivate: [AuthGuard],
        data: { roles: [UserRole.PRESCRIPTION_ADMIN, UserRole.DOCTOR] }
      },

      // Medical Records routes (accessible by DOCTOR)
      {
        path: 'view-records',
        component: ViewRecordsComponent,
        canActivate: [AuthGuard],
        data: { roles: [UserRole.DOCTOR] }
      },
      {
        path: 'add-records',
        component: AddRecordsComponent,
        canActivate: [AuthGuard],
        data: { roles: [UserRole.DOCTOR] }
      },
      {
        path: 'view-reports',
        component: ViewReportsComponent,
        canActivate: [AuthGuard],
        data: { roles: [UserRole.DOCTOR] }
      },

      // Common routes (accessible by all authenticated users)
      {
        path: 'view-doctors',
        component: DoctorsComponent,
        canActivate: [AuthGuard]
      },
      {
        path: 'settings',
        component: SettingsComponent,
        canActivate: [AuthGuard]
      }
    ]
  },

  // Wildcard route - redirect to login if not authenticated
  { path: '**', redirectTo: '/log' }
];
