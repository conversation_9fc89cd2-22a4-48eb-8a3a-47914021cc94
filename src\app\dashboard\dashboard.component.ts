import { Component, OnInit, <PERSON><PERSON><PERSON><PERSON>, Inject, ViewChild, ElementRef, AfterViewInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterLink } from '@angular/router';
import { PLATFORM_ID } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';
import { HttpClient } from '@angular/common/http';
import { Chart, registerables } from 'chart.js';

// Register all Chart.js components
Chart.register(...registerables);

@Component({
  selector: 'app-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.css'],
  standalone: true,
  imports: [RouterLink, CommonModule]
})
export class DashboardComponent implements OnInit, OnDestroy, AfterViewInit {
  @ViewChild('patientChart') patientChart!: ElementRef;
  private chart: any;

  currentTime: string = '';
  currentDate: string = '';
  humidity: string = '';
  temp: string = '';
  private timerId: any;

  constructor(
    @Inject(PLATFORM_ID) private platformId: any,
    private http: HttpClient
  ) {
    this.getCurrentLocation();
  }

  ngAfterViewInit() {
    this.initializeChart();
  }

  initializeChart() {
    if (!this.patientChart || !this.patientChart.nativeElement) {
      console.error('Chart canvas element not found');
      return;
    }

    const ctx = this.patientChart.nativeElement.getContext('2d');

    const data = {
      labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
      datasets: [
        {
          label: 'Total Patients',
          data: [1200, 1350, 1250, 1420, 1550, 1650],
          borderColor: '#4CAF50',
          backgroundColor: 'rgba(76, 175, 80, 0.1)',
          fill: true,
        },
        {
          label: 'Inpatients',
          data: [400, 450, 410, 480, 520, 550],
          borderColor: '#81C784',
          backgroundColor: 'rgba(129, 199, 132, 0.1)',
          fill: true,
        }
      ]
    };

    if (this.chart) {
      this.chart.destroy();
    }

    this.chart = new Chart(ctx, {
      type: 'line',
      data: data,
      options: {
        responsive: true,
        plugins: {
          legend: {
            display: false
          }
        },
        scales: {
          y: {
            beginAtZero: true,
            grid: {
              color: 'rgba(0, 0, 0, 0.05)'
            }
          },
          x: {
            grid: {
              display: false
            }
          }
        }
      }
    });
  }

  getWeather(location: string) {
    this.http
      .get<any>(`https://api.weatherapi.com/v1/current.json?key=ed07cfd651c0443d826163606240909&q=${location}&aqi=no`)
      .subscribe(data => {
        this.humidity = data.current.humidity + '%';
        this.temp = data.current.temp_c + '°C';
      });
  }

  getLocation(lat: number, lon: number) {
    this.http
      .get<any>(`https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lon}&zoom=10&addressdetails=1`)
      .subscribe(data => {
        const locationName = data.name || data.address?.city || data.display_name;
        this.getWeather(locationName);
      });
  }

  getCurrentLocation(): void {
    if (isPlatformBrowser(this.platformId)) {
      if ('geolocation' in navigator) {
        navigator.geolocation.getCurrentPosition(
          (position) => {
            const latitude = position.coords.latitude;
            const longitude = position.coords.longitude;
            this.getLocation(latitude, longitude);
          },
          (error) => {
            console.error('Error fetching location:', this.getErrorMessage(error));
            // Fallback to a default location
            this.getWeather('London');
          },
          {
            enableHighAccuracy: true,
            timeout: 15000,
            maximumAge: 0
          }
        );
      } else {
        console.error('Geolocation is not supported by this browser.');
        // Fallback to a default location
        this.getWeather('London');
      }
    }
  }

  getErrorMessage(error: GeolocationPositionError): string {
    switch (error.code) {
      case error.PERMISSION_DENIED:
        return 'User denied the request for Geolocation.';
      case error.POSITION_UNAVAILABLE:
        return 'Location information is unavailable.';
      case error.TIMEOUT:
        return 'The request to get user location timed out.';
      default:
        return 'An unknown error occurred.';
    }
  }

  ngOnInit(): void {
    if (isPlatformBrowser(this.platformId)) {
      this.timerId = setInterval(() => {
        const date = new Date();
        const hours = date.getHours();
        const minutes = date.getMinutes().toString().padStart(2, '0');
        const seconds = date.getSeconds().toString().padStart(2, '0');
        const ampm = hours >= 12 ? 'PM' : 'AM';
        const displayHours = hours % 12 || 12;

        this.currentTime = `${displayHours}:${minutes}:${seconds} ${ampm}`;
        this.currentDate = date.toLocaleDateString('en-US', {
          weekday: 'long',
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        });
      }, 1000);
    }
  }

  ngOnDestroy(): void {
    if (this.timerId) {
      clearInterval(this.timerId);
    }
    if (this.chart) {
      this.chart.destroy();
    }
  }
}
