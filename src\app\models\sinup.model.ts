export interface SignupRequest {
  username: string;
  email: string;
  phoneNumber: string;
  password: string;
  role: UserRole;
  specialization?: string | null;
}
export interface SignupResponse {
  success: boolean;
  message: string;
  user_id?: number;
  name?: string;
  email?: string;
}
export enum UserRole {
  OPD_ADMIN = 'OPD_ADMIN',
  PRESCRIPTION_ADMIN = 'PRESCRIPTION_ADMIN',
  DOCTOR = 'DOCTOR'
}
