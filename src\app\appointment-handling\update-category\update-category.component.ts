import { Component } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { HeaderAdminSectionComponent } from "../header-admin-section/header-admin-section.component";
import { RouterLink } from '@angular/router';

@Component({
  selector: 'app-update-category',
  standalone: true,
  imports: [FormsModule, CommonModule, HeaderAdminSectionComponent, RouterLink],
  templateUrl: './update-category.component.html',
  styleUrls: ['./update-category.component.css']
})
export class UpdateCategoryComponent {
  searchTerm: string = '';
  searchedCategory: any = { categoryId: null, description: '' };

  constructor(private http: HttpClient) {}

  searchCategory() {
    console.log("Searching for category with description: ", this.searchTerm);

    if (!this.searchTerm) {
      alert('Please enter a search term');
      return;
    }

    const params = { description: this.searchTerm };
    this.http.get<any[]>('http://localhost:8080/category/search', { params }).subscribe({
      next: (response) => {
        if (response.length > 0) {
          this.searchedCategory = response[0];
        } else {
          alert('Category not found');
          this.searchedCategory = { categoryId: null, description: '' };
        }
      },
      error: (err) => {
        console.error('Error fetching category:', err);
        alert('An error occurred while fetching the category');
      }
    });
  }

  updateCategory() {
    this.http.put(`http://localhost:8080/category/update/${this.searchedCategory.categoryId}`, this.searchedCategory, { responseType: 'text' })
      .subscribe({
        next: (response: string) => {
          console.log('Category updated successfully:', response);
          alert(response);
        },
        error: (error: any) => {
          console.error('Error updating category:', error);
          alert('Error updating category: ' + error.message);
        }
      });
  }


}
