import { Injectable } from '@angular/core';
import { Record } from './report-record-env.service';


@Injectable({
  providedIn: 'root'
})
export class RecordService {

  private currentRecord: Record | null = null;

  constructor() { }

  setCurrentRecord(record: Record) {
    this.currentRecord = record;
  }

  getCurrentRecord() {
    return this.currentRecord;
  }

  clearCurrentRecord() {
    this.currentRecord = null;
  }
}
