import { Injectable } from '@angular/core';
import { HttpClient, HttpErrorResponse, HttpHeaders } from '@angular/common/http';
import { Observable, catchError, map, throwError } from 'rxjs';
import { SignupRequest, SignupResponse, UserRole } from '../models/sinup.model';
import { environment } from '../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class SinupService {
  private readonly AUTH_URL = `${environment.AUTH_URL}/users/auth/register`;

  constructor(private http: HttpClient) { }

  signup(request: SignupRequest): Observable<SignupResponse> {
    const payload = {
      name: request.username,
      email: request.email,
      password: request.password,
      role: request.role,
      specialization: request.role === UserRole.DOCTOR ? (request.specialization || 'GENERAL_PRACTITIONER') : null
    };

    // Set proper headers
    const headers = new HttpHeaders({
      'Content-Type': 'application/json'
    });

    // Use responseType: 'text' to handle plain text responses
    return this.http.post(this.AUTH_URL, payload, {
      headers,
      responseType: 'text'
    }).pipe(
      map(response => {
        // Create a success response from the text response
        return {
          success: true,
          message: response || 'Signup successful'
        };
      }),
      catchError(this.handleError)
    );
  }

  private handleError(error: HttpErrorResponse): Observable<never> {
    let errorMessage = 'An unknown error occurred';

    // Check if the error has a text property (which might contain a successful response)
    if (error.error && typeof error.error === 'string' && error.status === 200) {
      // This is actually a success case with text response
      return throwError(() => ({
        success: true,
        message: error.error
      }));
    }

    if (error.error instanceof ErrorEvent) {
      // Client-side error
      errorMessage = `Error: ${error.error.message}`;
    } else if (error.status === 0) {
      // Network error
      errorMessage = 'Network error. Please check your connection and try again.';
    } else {
      // Server-side error
      errorMessage = error.error?.message || `Error Code: ${error.status}, Message: ${error.message}`;
    }

    console.error('Signup error:', error);
    return throwError(() => new Error(errorMessage));
  }
}
