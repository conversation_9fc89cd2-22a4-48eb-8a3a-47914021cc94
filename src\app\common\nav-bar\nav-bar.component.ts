// nav-bar.component.ts
import { Component } from '@angular/core';
import { Router, RouterLink } from '@angular/router';
import { RecordService } from '../../service/record.service';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-nav-bar',
  imports:[RouterLink,CommonModule],
  standalone:true,
  templateUrl: './nav-bar.component.html',
  styleUrls: ['./nav-bar.component.scss']
})
export class NavBarComponent {
  isSidebarCollapsed = false;
  currentOpenMenu: string | null = null;
  selectedItem: string | null = null;

  menuItems = [
    {
      id: 'patients',
      label: 'Patients',
      icon: 'bi bi-people-fill',
      submenu: [
        { id: 'view-patient', label: 'View Patient', routerLink: 'view-patient' },
        { id: 'add-patient', label: 'Add Patient', routerLink: 'add-patient' },
        { id: 'update-patient', label: 'Update Patient', routerLink: 'update-patient' }
      ]
    },
    {
      id: 'appointments',
      label: 'Appointments',
      icon: 'bi bi-calendar-check',
      submenu: [
        { id: 'view-appointment', label: 'View Appointments', routerLink: 'view-appointment' },
        { id: 'add-appointment', label: 'Add Appointment', routerLink: 'add-appointment' },
        { id: 'update-appointment', label: 'Update Appointment', routerLink: 'update-appointment' },
        { id: 'delete-appointment', label: 'Delete Appointment', routerLink: 'delete-appointment' }
      ]
    },
    {
      id: 'doctors',
      label: 'Doctors',
      icon: 'bi bi-person-workspace',
      routerLink: 'view-doctors'
    },
    {
      id: 'prescription',
      label: 'Prescription',
      icon: 'bi bi-file-earmark-medical',
      routerLink: 'prescription-management'
    },
    {
      id: 'category',
      label: 'Category',
      icon: 'bi bi-bookmarks-fill',
      submenu: [
        { id: 'view-category', label: 'View Category', routerLink: 'view-category' },
        { id: 'add-category', label: 'Add Category', routerLink: 'add-category' },
        { id: 'update-category', label: 'Update Category', routerLink: 'update-category' }
      ]
    },
    {
      id: 'reports',
      label: 'Reports',
      icon: 'bi bi-graph-up',
      submenu: [
        { id: 'view-reports', label: 'View Reports', routerLink: 'view-reports' }
      ]
    },
    {
      id: 'records',
      label: 'Records',
      icon: 'bi bi-folder-fill',
      submenu: [
        { id: 'view-records', label: 'View Records', routerLink: 'view-records' },
        { id: 'add-records', label: 'Add Records', routerLink: 'add-records' }
      ]
    }
  ];

  constructor(
    private router: Router,
    private recordService: RecordService
  ) { }

  toggleSidebar(): void {
    this.isSidebarCollapsed = !this.isSidebarCollapsed;
  }

  toggleMenu(menuId: string): void {
    if (this.currentOpenMenu === menuId) {
      this.currentOpenMenu = null;
    } else {
      this.currentOpenMenu = menuId;
    }
  }

  isMenuOpen(menuId: string): boolean {
    return this.currentOpenMenu === menuId;
  }

  setSelectedItem(itemId: string): void {
    this.selectedItem = itemId;
    if (itemId === 'add-records') {
      this.navigateToAddRecords();
    }
  }

  isItemSelected(itemId: string): boolean {
    return this.selectedItem === itemId;
  }

  navigateToAddRecords() {
    this.recordService.clearCurrentRecord();
    this.router.navigate(['add-records']);
  }
}
