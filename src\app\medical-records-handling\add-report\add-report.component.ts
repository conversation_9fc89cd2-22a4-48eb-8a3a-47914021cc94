import { CommonModule } from '@angular/common';
import { Component, Input, Output } from '@angular/core';
import { FormsModule, NgForm } from '@angular/forms';
import { ReportService, Report } from '../../service/report.service';
import { EventEmitter } from 'stream';
import { AddRecordsComponent } from '../add-records/add-records.component';


@Component({
  selector: 'app-add-report',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './add-report.component.html',
  styleUrl: './add-report.component.css'
})
export class AddReportComponent {
  public display: boolean = false
  @Input() addRecord!: AddRecordsComponent;

  public toggleAddReportDisplay() {
    this.display = !this.display;
    this.addRecord.loadReports();

  }
  public note: string = '';
  public report!: File; // File to be uploaded

  constructor(private reportService: ReportService) { }

  onFileSelected(event: any): void {
    if (event.target.files.length > 0) {
      this.report = event.target.files[0]; // Assign selected file
    }
  }

  submitReport(f: NgForm): void {
    if (!this.report) {
      alert('Please select a file to upload!');
      return;
    }

    this.reportService.addReport(this.note, this.report).subscribe({
      next: (response) => {
        console.log('Report added successfully:', response);
        alert('Report added successfully!');
        this.addRecord.loadReports();
        f.reset();
      },
      error: (error) => {
        console.error('Error adding report:', error);
        alert('Error adding report: ' + error);
      }
    });
  }
}

export interface Temp {
  t: string;
}
