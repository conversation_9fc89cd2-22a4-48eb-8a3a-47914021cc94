import { AsyncPipe, CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { Observable } from 'rxjs';
import { ReportService } from '../../service/report.service';
import { Report } from '../../service/report-record-env.service';
import { FormsModule } from '@angular/forms';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'app-view-reports',
  standalone: true,
  imports: [CommonModule, AsyncPipe,FormsModule, RouterLink],
  templateUrl: './view-reports.component.html',
  styleUrl: './view-reports.component.css'
})

export class ViewReportsComponent {

  // Observable to hold the list of reports
  reports$?: Observable<Report[]>
  reportId: string | null = null;
  filterDate: string | null = null;

  constructor(private reportService: ReportService) {
    this.loadReports();
  }

  // Method to fetch reports
  loadReports(): void {
    this.reports$ = this.reportService.getAllReports();
  }


  isModalOpen = false;
  selectedReport: Report | null = null;

  // Opens the modal with the selected record's details
  openModal(report: Report) {
    this.selectedReport = report;
    this.isModalOpen = true;
  }

  // Closes the modal
  closeModal() {
    this.isModalOpen = false;
    this.selectedReport = null;
  }

  downloadReport(reportLink: string | undefined) {
    if (reportLink) {
      this.reportService.downloadReport(reportLink)
      console.log("download called" + reportLink)
    }
  }
  deleteReport(reportId: string): void {
    this.reportService.deleteReport(reportId).subscribe({
      next: (response) => {
        console.log('Report deleted successfully:', response);
        // Optionally reload or refresh the list of reports
      },
      error: (error) => {
        console.error('Failed to delete report:', error);
      }
    });
  }
  filter(){
    if (this.reportId != null && this.filterDate == null) {
      this.reports$ = this.reportService.searchById(this.reportId);

    } else if (this.reportId == null && this.filterDate != null) {
      this.reports$ = this.reportService.searchByDate(this.filterDate);

    } else if (this.reportId != null && this.filterDate != null) {
      this.reports$ = this.reportService.searchByIdAndDate(this.reportId, this.filterDate);

    } else if (this.reportId == null && this.filterDate == null) {
      this.loadReports();
    }
  }
  clear(){
    this.reportId = null;
    this.filterDate = null;
    this.loadReports();
    if (this.isModalOpen) {
      this.closeModal();
    }
  }
}
