export interface User {
  id?: string;
  username: string;
  role: UserRole;
  token?: string;
}
export interface LoginRequest {
  username: string;
  password: string;
}
export interface LoginResponse {
  user: User;
  token: string;
  success: boolean;
  message?: string;
}

export enum UserRole {
  OPD_ADMIN = 'OPD_ADMIN',
  PRESCRIPTION_ADMIN = 'PRESCRIPTION_ADMIN',
  DOCTOR = 'DOCTOR'
}













