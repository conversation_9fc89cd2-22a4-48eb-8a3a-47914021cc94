export interface User {
  id?: string;
  username: string;
  role: UserRole;
  token?: string;
}

export interface LoginRequest {
  username: string;
  password: string;
}

// API Response structure matching the backend
export interface LoginApiResponse {
  message: string;
  data: {
    jwtToken: string;
    user_id: number;
    role: UserRole;
  };
}

// Internal response structure for the service
export interface LoginResponse {
  user: User;
  token: string;
  success: boolean;
  message?: string;
}

export enum UserRole {
  OPD_ADMIN = 'OPD_ADMIN',
  PRESCRIPTION_ADMIN = 'PRESCRIPTION_ADMIN',
  DOCTOR = 'DOCTOR'
}













