/* Container and Card */
.signup-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 20px;
}

.signup-card {
  width: 100%;
  max-width: 500px;
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 4px 12px rgba(11, 105, 30, 0.2);
  padding: 30px;
}

.signup-header {
  text-align: center;
  margin-bottom: 30px;
}

.signup-header h2 {
  color: #38823E;
  margin-bottom: 10px;
  font-weight: 600;
}

.signup-header p {
  color: #6c757d;
  font-size: 14px;
}

/* Error Message */
.error-message {
  background-color: #f8d7da;
  color: #721c24;
  padding: 10px;
  border-radius: 5px;
  margin-bottom: 20px;
  font-size: 14px;
}

/* Form Groups */
.form-group {
  margin-bottom: 20px;
}

/* Labels */
.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #0c5e07;
}

/* Input Container */
.input-container {
  display: flex;
  align-items: center;
  border: 1px solid #80CD84;
  border-radius: 5px;
  padding: 0 12px;
  transition: all 0.3s;
}

.input-container:focus-within {
  border-color: #38823E;
  box-shadow: 0 0 0 2px rgba(56, 130, 62, 0.25);
}

.input-container svg {
  color: #80CD84;
  margin-right: 10px;
}

.input-container input,
.input-container select {
  flex: 1;
  border: none;
  outline: none;
  padding: 12px 0;
  font-size: 14px;
  background: transparent;
}

.input-container input::placeholder {
  color: #80CD84;
}

/* Role Selection */
.role-selection {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.role-option {
  flex: 1;
  min-width: 120px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 15px;
  border: 1px solid #80CD84;
  border-radius: 5px;
  cursor: pointer;
  transition: all 0.3s;
}

.role-option:hover {
  background-color: #dcf4da;
}

.role-option.selected {
  border-color: #38823E;
  background-color: rgba(56, 130, 62, 0.1);
}

.role-option svg {
  color: #38823E;
  margin-bottom: 8px;
}

.role-option span {
  font-size: 14px;
  font-weight: 500;
  color: #0c5e07;
}

/* Specialization Dropdown */
select {
  cursor: pointer;
  border-radius: 6px;
  padding: 8px 10px;
  font-weight: 500;
  background-color: #fff;
  border: 1.8px solid #80CD84;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  background-image: url("data:image/svg+xml;charset=US-ASCII,%3Csvg%20width%3D'10'%20height%3D'6'%20viewBox%3D'0%200%2010%206'%20xmlns%3D'http%3A//www.w3.org/2000/svg'%3E%3Cpath%20d%3D'M0%200l5%206%205-6z'%20fill%3D'%2380CD84'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 12px center;
  background-size: 10px 6px;
}

select:focus {
  border-color: #38823E;
  box-shadow: 0 0 0 0.2rem rgba(56, 130, 62, 0.25);
}

select:disabled {
  background-color: #f9f9f9;
  color: #aaa;
  cursor: not-allowed;
}

/* Submit Button */
button[type="submit"] {
  width: 100%;
  padding: 12px;
  background-color: #38823E;
  color: white;
  border: none;
  border-radius: 5px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s;
}

button[type="submit"]:hover {
  background-color: #0c5e07;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(11, 105, 30, 0.2);
}

button[type="submit"]:disabled {
  background-color: #95a5a6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Responsive */
@media (max-width: 480px) {
  .signup-card {
    padding: 30px 20px;
    width: 100%;
  }

  .role-selection {
    flex-direction: column;
  }
}

/* Add loading spinner */
.spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s ease-in-out infinite;
  margin-right: 10px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Login link */
.login-link {
  text-align: center;
  margin-top: 20px;
  font-size: 14px;
  color: #6c757d;
}

.login-link a {
  color: #38823E;
  text-decoration: none;
  font-weight: 500;
}

.login-link a:hover {
  text-decoration: underline;
  color: #0c5e07;
}

/* Signup Image Styling */
.signup-image {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 40px;
  position: relative;
}

.signup-image-display {
  width: 900px;
  height: 900px;
  border-radius: 16px;
  box-shadow: 0 15px 30px rgba(11, 105, 30, 0.2);
  transition: all 0.5s ease;
  object-fit: cover;
  border: 4px solid white;
}

.signup-image-display:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(11, 105, 30, 0.25);
}

@media (max-width: 1200px) {
  .signup-image-display {
    width: 400px;
    height: 500px;
  }
}

@media (max-width: 992px) {
  .signup-image-display {
    width: 350px;
    height: 450px;
  }
}

@media (max-width: 768px) {
  .signup-container {
    flex-direction: column;
  }

  .signup-image {
    margin-right: 0;
    margin-bottom: 30px;
  }

  .signup-image-display {
    width: 300px;
    height: 380px;
  }
}

@media (max-width: 480px) {
  .signup-image-display {
    width: 280px;
    height: 350px;
  }
}
