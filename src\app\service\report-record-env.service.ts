import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class ReportRecordEnvService {
  public readonly MAIN_URL: string = 'http://localhost:8083';
  constructor() { }
}

export interface Report {
  reportId: string; // Unique ID of the report
  patientId: string; // Patient's ID
  reportLink: string; // Link to download or access the report
  categoryType: string; // Type or category of the report
  date: string; // Creation or submission date (ISO format: yyyy-MM-dd)
  reportDate: string; // Date of the report itself (ISO format: yyyy-MM-dd)
  note: string; // Additional notes related to the report
  recordList?: Record[]; // List of associated records (optional)
  isChecked?: boolean; //Flag to track whether the checkbox is checked (default: false)
}

export interface Record {
  recordId: string; // Unique ID of the record
  patientID: string; // Pat<PERSON>'s ID linked to the record
  recordDate: string; // Date of the record (ISO format: yyyy-MM-dd)
  description: string; // Description of the record (at least 10 characters long)
  reportList?: Report[]; // List of associated reports (optional)
}

