import { Component, inject } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { Router, RouterLink } from '@angular/router';
import { CommonModule } from '@angular/common';
import { UserRole } from '../models/sinup.model';
import { SignupRequest } from '../models/sinup.model';
import { SinupService } from '../service/sinup.service';

@Component({
  selector: 'app-sinup',
  standalone: true,
  imports: [FormsModule, CommonModule, RouterLink],
  templateUrl: './sinup.component.html',
  styleUrl: './sinup.component.css'
})
export class SinupComponent {
  // Expose UserRole enum to template
  UserRole = UserRole;

  signupData: SignupRequest = {
    username: '',
    email: '',
    phoneNumber: '',
    password: '',
    role: UserRole.DOCTOR,
    specialization: ''
  };

  confirmPassword: string = '';
  showSpecialization: boolean = true;
  isLoading: boolean = false;
  errorMessage: string = '';

  // List of specializations for doctors
  specializations: string[] = [
    'GENERAL_PRACTITIONER',
    'CARDIOLOGIST',
    'DERMATOLOGIST',
    'NEUROLOGIST',
    'PEDIATRICIAN',
    'ORTHOPEDIC_SURGEON',
    'GYNECOLOGIST',
    'PSYCHIATRIST',
    'ONCOLOGIST',
    'ENT_SPECIALIST',
    'OPHTHALMOLOGIST',
    'UROLOGIST',
    'RADIOLOGIST',
    'ANESTHESIOLOGIST'

  ];

  private router = inject(Router);
  private sinupService = inject(SinupService);

  onRoleChange(): void {
    // Show specialization field only for doctors
    this.showSpecialization = this.signupData.role === UserRole.DOCTOR;

    // Clear specialization if not a doctor
    if (!this.showSpecialization) {
      this.signupData.specialization = null;
    }
  }

  onSubmit(): void {
    if (this.signupData.password !== this.confirmPassword) {
      this.errorMessage = 'Passwords do not match';
      return;
    }

    this.isLoading = true;
    this.errorMessage = '';

    // Ensure specialization is null for non-doctors
    if (this.signupData.role !== UserRole.DOCTOR) {
      this.signupData.specialization = null;
    }

    this.sinupService.signup(this.signupData).subscribe({
      next: (response) => {
        this.isLoading = false;
        if (response.success) {
          // Navigate to login page after successful registration
          this.router.navigate(['/log']);
        } else {
          this.errorMessage = response.message || 'Signup failed';
        }
      },
      error: (error) => {
        this.isLoading = false;
        // Check if the error is actually a success response
        if (error && error.success) {
          this.router.navigate(['/log']);
          return;
        }
        this.errorMessage = error.message || 'An error occurred during signup';
      }
    });
  }

  getRoleDisplay(role: UserRole): string {
    switch(role) {
      case UserRole.DOCTOR:
        return 'Doctor';
      case UserRole.OPD_ADMIN:
        return 'OPD Admin';
      case UserRole.PRESCRIPTION_ADMIN:
        return 'Prescription Admin';

    }
  }
}
