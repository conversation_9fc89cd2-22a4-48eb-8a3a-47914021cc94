.date-section {
    align-items: center;
    background: #ffffff;
    padding: 10px 20px;
    gap: 15px;
    box-shadow: -2px 2px 4px rgba(140, 156, 142, 0.2),
      2px -2px 4px rgba(140, 156, 142, 0.2),
      -2px -2px 4px rgba(255, 255, 255, 0.9), 2px 2px 5px rgba(140, 156, 142, 0.9),
      inset 1px 1px 2px rgba(255, 255, 255, 0.3),
      inset -1px -1px 2px rgba(140, 156, 142, 0.5);
    border-radius: 40px;
  }
  
  .date-info {
    font-family: "Poppins", sans-serif;
    font-size: 14px;
  }
  
  .admin-info {
    display: flex;
    align-items: center;
  }
  
  .admin-photo {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: url("https://via.placeholder.com/50") no-repeat center center;
    background-size: cover;
  }
  
  .admin-details {
    margin-left: 10px;
  }
  
  .admin-name {
    font-family: "Poppins", sans-serif;
    font-weight: 600;
    font-size: 16px;
    color: #010d02;
  }
  
  .admin-role {
    font-family: "Poppins", sans-serif;
    font-weight: 600;
    font-size: 14px;
    color: rgba(1, 13, 2, 0.5);
  }
  