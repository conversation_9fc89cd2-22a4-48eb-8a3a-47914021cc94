import { CommonModule } from '@angular/common';
import { HttpClient, HttpClientModule } from '@angular/common/http';
import { Component, OnInit } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { HeaderAdminSectionComponent } from "../header-admin-section/header-admin-section.component";
import { RouterLink } from '@angular/router';

@Component({
  selector: 'app-update-appointment',
  standalone: true,
  imports: [FormsModule, CommonModule, RouterLink],
  templateUrl: './update-appointment.component.html',
  styleUrl: './update-appointment.component.css'
})
export class UpdateAppointmentComponent  {
  public searchText:any="";
  isDataLaod: Boolean = false;

  constructor(private http:HttpClient){}

  appintmentDetails:any = [];
  appintmentDetailsTemp:any = [];
  patientDetails:any = {
    firstName: "",
    lastName: "",
    nic: "",
    contactNo: "",
    email: "",
    gender: ""
  };

  genderChecker(){
    if (this.patientDetails.gender?.toLowerCase() === "female") {
      return true;
    } else {
      return false;
    }
  }

  searchPatient(){
    //Try searching by appointment ID first
    this.http.get<any>(`http://localhost:8080/appointment/search-by-appointmentId/${this.searchText}`).subscribe(res=>{
      this.appintmentDetails = res;
      if (this.appintmentDetails.length > 0) {
        this.http.get<any>(`http://localhost:8081/patient/patient-search-by-id/${this.appintmentDetails[0].patientId}`).subscribe(res=>{
          this.patientDetails = res;
          this.toggleDataLoad();
        });
      } else {
        this.http.get<any>(`http://localhost:8081/patient/patient-search-by-id/${this.searchText}`).subscribe(res=>{
          this.patientDetails = res;
          if (this.patientDetails) {
            this.http.get<any>(`http://localhost:8080/appointment/search-by-patientId/${this.patientDetails.id}`).subscribe(res=>{
              this.appintmentDetails = res;
              this.toggleDataLoad()
            });
          }
        });
      }
    });
  }

  update() {
    this.appintmentDetailsTemp = this.appintmentDetails[0];
    this.http.put("http://localhost:8080/appointment/update", this.appintmentDetailsTemp).subscribe({
      next: (res) => {
        console.log('Update successful:', res);
        this.clearFields();
      },
      error: (err) => {
        console.error('Error updating appointment:', err);
      }
    });
  }

  clearFields(){
    this.appintmentDetailsTemp = [];
    this.appintmentDetails[0] = [];
    this.patientDetails = {
      firstName: "",
      lastName: "",
      nic: "",
      contactNo: "",
      email: "",
      gender: ""
    };
  }

  toggleDataLoad(){
    this.isDataLaod = !this.isDataLaod;
  }
}
