{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"HMS": {"projectType": "application", "schematics": {}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": "dist/hms", "index": "src/index.html", "browser": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "assets": [{"input": "src/app/prescription-management/img", "glob": "delete.png", "output": "img"}, {"glob": "**/*", "input": "public"}], "styles": ["src/styles.css", "node_modules/bootstrap/dist/css/bootstrap.min.css", "node_modules/bootstrap-icons/font/bootstrap-icons.css"], "scripts": ["node_modules/bootstrap/dist/js/bootstrap.bundle.min.js"], "allowedCommonJsDependencies": ["canvg", "rgbcolor", "raf", "core-js", "@babel/runtime/helpers/asyncToGenerator", "@babel/runtime/helpers/defineProperty", "@babel/runtime/helpers/typeof", "jspdf"], "optimization": {"scripts": true, "styles": {"minify": true, "inlineCritical": true}, "fonts": {"inline": false}}, "server": "src/main.server.ts", "prerender": true, "ssr": {"entry": "server.ts"}}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "2MB", "maximumError": "5MB"}, {"type": "anyComponentStyle", "maximumWarning": "100kB", "maximumError": "120kB"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "HMS:build:production"}, "development": {"buildTarget": "HMS:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "assets": [{"glob": "/*", "input": "public"}], "styles": ["src/styles.css", "./node_modules/bootstrap/dist/css/bootstrap.min.css"], "scripts": ["./node_modules/bootstrap/dist/js/bootstrap.min.js"]}}, "server": {"builder": "@angular-devkit/build-angular:server", "options": {"outputPath": "dist/hms/server", "main": "server.ts", "tsConfig": "tsconfig.server.json"}}}}}, "cli": {"analytics": false}}