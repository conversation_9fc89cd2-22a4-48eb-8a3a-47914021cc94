import { Injectable } from '@angular/core';
import { CanActivate, ActivatedRouteSnapshot, RouterStateSnapshot, Router } from '@angular/router';
import { Observable } from 'rxjs';
import { LoginService } from '../../service/auth/login.service';
import { UserRole } from '../../models/auth/login.model';

@Injectable({
  providedIn: 'root'
})
export class AuthGuard implements CanActivate {

  constructor(
    private loginService: LoginService,
    private router: Router
  ) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> | Promise<boolean> | boolean {

    const currentUser = this.loginService.currentUserValue;

    if (currentUser) {
      // Check if route has role requirements
      const requiredRoles = route.data?.['roles'] as UserRole[];

      if (requiredRoles && requiredRoles.length > 0) {
        // Check if user has required role
        if (requiredRoles.includes(currentUser.role)) {
          return true;
        } else {
          // User doesn't have required role, redirect to unauthorized page
          this.router.navigate(['/error-page']);
          return false;
        }
      }

      // No specific role required, user is authenticated
      return true;
    }

    // User is not authenticated, redirect to login
    this.router.navigate(['/log'], { queryParams: { returnUrl: state.url } });
    return false;
  }
}
